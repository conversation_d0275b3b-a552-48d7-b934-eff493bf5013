import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { RootStackParamList } from '../navigation/AppNavigator';
import { intermediateLessons } from '../data/lessons';
import LessonCard from '../components/LessonCard';
import ProgressBar from '../components/ProgressBar';
import { Colors } from '../constants/theme';

type IntermediateScreenNavigationProp = StackNavigationProp<RootStackParamList>;

export default function IntermediateScreen() {
  const navigation = useNavigation<IntermediateScreenNavigationProp>();

  const completedLessons = intermediateLessons.filter(lesson => lesson.completed).length;
  const progressPercentage = (completedLessons / intermediateLessons.length) * 100;

  const renderLessonItem = ({ item }: { item: typeof intermediateLessons[0] }) => (
    <LessonCard
      title={item.title}
      somaliTitle={item.somaliTitle}
      description={item.description}
      somaliDescription={item.somaliDescription}
      duration={item.duration}
      completed={item.completed}
      levelColor={Colors.intermediate}
      onPress={() =>
        navigation.navigate('Lesson', {
          lessonId: item.id,
          level: 'intermediate',
          title: item.title,
        })
      }
    />
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Dhexe - Intermediate</Text>
        <Text style={styles.headerSubtitle}>
          Sii wad barashada Ingiriisiga
        </Text>
        <Text style={styles.headerDescription}>
          Continue your English learning journey
        </Text>
      </View>

      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>Horumarkaaga - Your Progress</Text>
        <ProgressBar
          progress={progressPercentage}
          color={Colors.intermediate}
          showPercentage={true}
        />
        <Text style={styles.progressLabel}>
          {completedLessons} / {intermediateLessons.length} casharrada
        </Text>
      </View>

      <FlatList
        data={intermediateLessons}
        renderItem={renderLessonItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.lessonsList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#FF9800',
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#FFF3E0',
    marginBottom: 3,
  },
  headerDescription: {
    fontSize: 14,
    color: '#FFF3E0',
  },
  progressContainer: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  progressText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FF9800',
    borderRadius: 4,
  },
  progressLabel: {
    fontSize: 14,
    color: '#666',
  },
  lessonsList: {
    padding: 15,
  },
  lessonCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  completedCard: {
    backgroundColor: '#FFF3E0',
    borderColor: '#FF9800',
    borderWidth: 1,
  },
  lessonContent: {
    padding: 20,
  },
  lessonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  lessonTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  lessonDuration: {
    fontSize: 14,
    color: '#FF9800',
    fontWeight: 'bold',
  },
  lessonSomaliTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  lessonDescription: {
    fontSize: 14,
    color: '#333',
    marginBottom: 3,
  },
  lessonSomaliDescription: {
    fontSize: 13,
    color: '#666',
  },
  completedBadge: {
    marginTop: 10,
    alignSelf: 'flex-start',
  },
  completedText: {
    fontSize: 12,
    color: '#FF9800',
    fontWeight: 'bold',
  },
});
