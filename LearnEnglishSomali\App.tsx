/**
 * Learn English in Somali App
 * React Native Mobile Application
 *
 * @format
 */

import React from 'react';
import { StatusBar, useColorScheme } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import AppNavigator from './src/navigation/AppNavigator';
import { ProgressProvider } from './src/context/ProgressContext';

function App() {
  const isDarkMode = useColorScheme() === 'dark';

  return (
    <SafeAreaProvider>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor="#2E8B57"
      />
      <ProgressProvider>
        <AppNavigator />
      </ProgressProvider>
    </SafeAreaProvider>
  );
}

export default App;
