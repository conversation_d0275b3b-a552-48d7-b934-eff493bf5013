import AsyncStorage from '@react-native-async-storage/async-storage';

export interface UserProgress {
  lessonId: string;
  completed: boolean;
  completedAt?: string;
  score?: number;
  timeSpent?: number; // in minutes
}

export interface UserStats {
  totalLessonsCompleted: number;
  totalTimeSpent: number; // in minutes
  currentStreak: number;
  longestStreak: number;
  lastStudyDate?: string;
  achievements: string[];
}

const STORAGE_KEYS = {
  USER_PROGRESS: 'user_progress',
  USER_STATS: 'user_stats',
  USER_PREFERENCES: 'user_preferences',
};

class StorageService {
  // Progress Management
  async getUserProgress(): Promise<UserProgress[]> {
    try {
      const progressData = await AsyncStorage.getItem(STORAGE_KEYS.USER_PROGRESS);
      return progressData ? JSON.parse(progressData) : [];
    } catch (error) {
      console.error('Error getting user progress:', error);
      return [];
    }
  }

  async updateLessonProgress(lessonId: string, completed: boolean, score?: number, timeSpent?: number): Promise<void> {
    try {
      const currentProgress = await this.getUserProgress();
      const existingIndex = currentProgress.findIndex(p => p.lessonId === lessonId);
      
      const progressItem: UserProgress = {
        lessonId,
        completed,
        completedAt: completed ? new Date().toISOString() : undefined,
        score,
        timeSpent,
      };

      if (existingIndex >= 0) {
        currentProgress[existingIndex] = progressItem;
      } else {
        currentProgress.push(progressItem);
      }

      await AsyncStorage.setItem(STORAGE_KEYS.USER_PROGRESS, JSON.stringify(currentProgress));
      
      // Update stats if lesson was completed
      if (completed) {
        await this.updateUserStats(timeSpent || 0);
      }
    } catch (error) {
      console.error('Error updating lesson progress:', error);
    }
  }

  async isLessonCompleted(lessonId: string): Promise<boolean> {
    try {
      const progress = await this.getUserProgress();
      const lessonProgress = progress.find(p => p.lessonId === lessonId);
      return lessonProgress?.completed || false;
    } catch (error) {
      console.error('Error checking lesson completion:', error);
      return false;
    }
  }

  // Stats Management
  async getUserStats(): Promise<UserStats> {
    try {
      const statsData = await AsyncStorage.getItem(STORAGE_KEYS.USER_STATS);
      return statsData ? JSON.parse(statsData) : {
        totalLessonsCompleted: 0,
        totalTimeSpent: 0,
        currentStreak: 0,
        longestStreak: 0,
        achievements: [],
      };
    } catch (error) {
      console.error('Error getting user stats:', error);
      return {
        totalLessonsCompleted: 0,
        totalTimeSpent: 0,
        currentStreak: 0,
        longestStreak: 0,
        achievements: [],
      };
    }
  }

  async updateUserStats(timeSpent: number): Promise<void> {
    try {
      const currentStats = await this.getUserStats();
      const today = new Date().toDateString();
      const lastStudyDate = currentStats.lastStudyDate;
      
      // Update basic stats
      currentStats.totalLessonsCompleted += 1;
      currentStats.totalTimeSpent += timeSpent;
      currentStats.lastStudyDate = today;

      // Update streak
      if (lastStudyDate) {
        const lastDate = new Date(lastStudyDate);
        const todayDate = new Date(today);
        const daysDiff = Math.floor((todayDate.getTime() - lastDate.getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysDiff === 1) {
          // Consecutive day
          currentStats.currentStreak += 1;
        } else if (daysDiff > 1) {
          // Streak broken
          currentStats.currentStreak = 1;
        }
        // If daysDiff === 0, it's the same day, don't change streak
      } else {
        // First time studying
        currentStats.currentStreak = 1;
      }

      // Update longest streak
      if (currentStats.currentStreak > currentStats.longestStreak) {
        currentStats.longestStreak = currentStats.currentStreak;
      }

      // Check for achievements
      await this.checkAndAddAchievements(currentStats);

      await AsyncStorage.setItem(STORAGE_KEYS.USER_STATS, JSON.stringify(currentStats));
    } catch (error) {
      console.error('Error updating user stats:', error);
    }
  }

  private async checkAndAddAchievements(stats: UserStats): Promise<void> {
    const newAchievements: string[] = [];

    // First lesson achievement
    if (stats.totalLessonsCompleted === 1 && !stats.achievements.includes('first_lesson')) {
      newAchievements.push('first_lesson');
    }

    // Milestone achievements
    if (stats.totalLessonsCompleted === 5 && !stats.achievements.includes('five_lessons')) {
      newAchievements.push('five_lessons');
    }

    if (stats.totalLessonsCompleted === 10 && !stats.achievements.includes('ten_lessons')) {
      newAchievements.push('ten_lessons');
    }

    // Streak achievements
    if (stats.currentStreak === 3 && !stats.achievements.includes('three_day_streak')) {
      newAchievements.push('three_day_streak');
    }

    if (stats.currentStreak === 7 && !stats.achievements.includes('week_streak')) {
      newAchievements.push('week_streak');
    }

    // Time-based achievements
    if (stats.totalTimeSpent >= 60 && !stats.achievements.includes('one_hour_study')) {
      newAchievements.push('one_hour_study');
    }

    if (stats.totalTimeSpent >= 300 && !stats.achievements.includes('five_hours_study')) {
      newAchievements.push('five_hours_study');
    }

    // Add new achievements
    stats.achievements.push(...newAchievements);
  }

  // Utility methods
  async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.USER_PROGRESS,
        STORAGE_KEYS.USER_STATS,
        STORAGE_KEYS.USER_PREFERENCES,
      ]);
    } catch (error) {
      console.error('Error clearing data:', error);
    }
  }

  async exportUserData(): Promise<string> {
    try {
      const progress = await this.getUserProgress();
      const stats = await this.getUserStats();
      
      return JSON.stringify({
        progress,
        stats,
        exportedAt: new Date().toISOString(),
      }, null, 2);
    } catch (error) {
      console.error('Error exporting user data:', error);
      return '';
    }
  }
}

export default new StorageService();
