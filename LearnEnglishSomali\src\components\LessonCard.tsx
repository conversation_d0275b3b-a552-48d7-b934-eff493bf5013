import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

interface LessonCardProps {
  title: string;
  somaliTitle: string;
  description: string;
  somaliDescription: string;
  duration: string;
  completed: boolean;
  onPress: () => void;
  levelColor?: string;
}

export default function LessonCard({
  title,
  somaliTitle,
  description,
  somaliDescription,
  duration,
  completed,
  onPress,
  levelColor = '#4CAF50',
}: LessonCardProps) {
  return (
    <TouchableOpacity
      style={[
        styles.card,
        completed && styles.completedCard,
        completed && { borderColor: levelColor },
      ]}
      onPress={onPress}
      activeOpacity={0.7}>
      <View style={styles.cardContent}>
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
          <Text style={[styles.duration, { color: levelColor }]}>{duration}</Text>
        </View>
        
        <Text style={styles.somaliTitle}>{somaliTitle}</Text>
        
        <Text style={styles.description}>{description}</Text>
        <Text style={styles.somaliDescription}>{somaliDescription}</Text>
        
        {completed && (
          <View style={styles.completedBadge}>
            <Text style={[styles.completedText, { color: levelColor }]}>
              ✓ Dhamaystiran
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  completedCard: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
  },
  cardContent: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  duration: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  somaliTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#333',
    marginBottom: 3,
  },
  somaliDescription: {
    fontSize: 13,
    color: '#666',
  },
  completedBadge: {
    marginTop: 10,
    alignSelf: 'flex-start',
  },
  completedText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
});
