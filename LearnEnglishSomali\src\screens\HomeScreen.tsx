import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import type { TabParamList } from '../navigation/AppNavigator';

type HomeScreenNavigationProp = BottomTabNavigationProp<TabParamList, 'Home'>;

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const navigation = useNavigation<HomeScreenNavigationProp>();

  const levelCards = [
    {
      title: 'Bilowga',
      englishTitle: 'Beginner',
      description: 'Bilow barashada Ingiriisiga ah',
      englishDescription: 'Start learning English basics',
      color: '#4CAF50',
      screen: 'Beginner' as keyof TabParamList,
    },
    {
      title: 'Dhexe',
      englishTitle: 'Intermediate',
      description: '<PERSON>i wad barashada Ingiriisiga',
      englishDescription: 'Continue your English journey',
      color: '#FF9800',
      screen: 'Intermediate' as keyof TabParamList,
    },
    {
      title: 'Horumar',
      englishTitle: 'Advanced',
      description: 'Ku dhamaystir xirfaddaada Ingiriisiga',
      englishDescription: 'Master your English skills',
      color: '#F44336',
      screen: 'Advanced' as keyof TabParamList,
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.welcomeText}>Soo dhawoow!</Text>
        <Text style={styles.welcomeSubtext}>Welcome to English Learning</Text>
        <Text style={styles.description}>
          Baro Ingiriisiga si fudud oo Soomaali ah
        </Text>
        <Text style={styles.descriptionEnglish}>
          Learn English easily in Somali
        </Text>
      </View>

      <View style={styles.levelsContainer}>
        <Text style={styles.sectionTitle}>Dooro heerkaaga</Text>
        <Text style={styles.sectionSubtitle}>Choose your level</Text>
        
        {levelCards.map((level, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.levelCard, { backgroundColor: level.color }]}
            onPress={() => navigation.navigate(level.screen)}
            activeOpacity={0.8}>
            <View style={styles.cardContent}>
              <Text style={styles.cardTitle}>{level.title}</Text>
              <Text style={styles.cardEnglishTitle}>{level.englishTitle}</Text>
              <Text style={styles.cardDescription}>{level.description}</Text>
              <Text style={styles.cardEnglishDescription}>
                {level.englishDescription}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Bilow maanta oo baro Ingiriisiga!
        </Text>
        <Text style={styles.footerEnglishText}>
          Start today and learn English!
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2E8B57',
    padding: 20,
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  welcomeSubtext: {
    fontSize: 18,
    color: '#E8F5E8',
    marginBottom: 15,
  },
  description: {
    fontSize: 16,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 5,
  },
  descriptionEnglish: {
    fontSize: 14,
    color: '#E8F5E8',
    textAlign: 'center',
  },
  levelsContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  levelCard: {
    borderRadius: 12,
    marginBottom: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  cardContent: {
    padding: 20,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 3,
  },
  cardEnglishTitle: {
    fontSize: 18,
    color: '#E8F5E8',
    marginBottom: 10,
  },
  cardDescription: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 3,
  },
  cardEnglishDescription: {
    fontSize: 14,
    color: '#E8F5E8',
  },
  footer: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: '#fff',
    marginTop: 20,
  },
  footerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E8B57',
    marginBottom: 5,
  },
  footerEnglishText: {
    fontSize: 16,
    color: '#666',
  },
});
