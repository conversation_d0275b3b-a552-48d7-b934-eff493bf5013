import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { RootStackParamList } from '../navigation/AppNavigator';
import { beginnerLessons } from '../data/lessons';
import LessonCard from '../components/LessonCard';
import ProgressBar from '../components/ProgressBar';
import { Colors } from '../constants/theme';
import { useProgress } from '../context/ProgressContext';

type BeginnerScreenNavigationProp = StackNavigationProp<RootStackParamList>;

export default function BeginnerScreen() {
  const navigation = useNavigation<BeginnerScreenNavigationProp>();
  const { isLessonCompleted } = useProgress();

  const completedLessons = beginnerLessons.filter(lesson => isLessonCompleted(lesson.id)).length;
  const progressPercentage = (completedLessons / beginnerLessons.length) * 100;

  const renderLessonItem = ({ item }: { item: typeof beginnerLessons[0] }) => (
    <LessonCard
      title={item.title}
      somaliTitle={item.somaliTitle}
      description={item.description}
      somaliDescription={item.somaliDescription}
      duration={item.duration}
      completed={isLessonCompleted(item.id)}
      levelColor={Colors.beginner}
      onPress={() =>
        navigation.navigate('Lesson', {
          lessonId: item.id,
          level: 'beginner',
          title: item.title,
        })
      }
    />
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Bilowga - Beginner</Text>
        <Text style={styles.headerSubtitle}>
          Bilow barashada Ingiriisiga ah
        </Text>
        <Text style={styles.headerDescription}>
          Start your English learning journey
        </Text>
      </View>

      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>Horumarkaaga - Your Progress</Text>
        <ProgressBar
          progress={progressPercentage}
          color={Colors.beginner}
          showPercentage={true}
        />
        <Text style={styles.progressLabel}>
          {completedLessons} / {beginnerLessons.length} casharrada
        </Text>
      </View>

      <FlatList
        data={beginnerLessons}
        renderItem={renderLessonItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.lessonsList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#4CAF50',
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#E8F5E8',
    marginBottom: 3,
  },
  headerDescription: {
    fontSize: 14,
    color: '#E8F5E8',
  },
  progressContainer: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  progressText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  progressLabel: {
    fontSize: 14,
    color: '#666',
  },
  lessonsList: {
    padding: 15,
  },
  lessonCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  completedCard: {
    backgroundColor: '#E8F5E8',
    borderColor: '#4CAF50',
    borderWidth: 1,
  },
  lessonContent: {
    padding: 20,
  },
  lessonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  lessonTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  lessonDuration: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  lessonSomaliTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  lessonDescription: {
    fontSize: 14,
    color: '#333',
    marginBottom: 3,
  },
  lessonSomaliDescription: {
    fontSize: 13,
    color: '#666',
  },
  completedBadge: {
    marginTop: 10,
    alignSelf: 'flex-start',
  },
  completedText: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
});
