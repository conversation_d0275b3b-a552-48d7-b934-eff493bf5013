export const Colors = {
  // Primary colors
  primary: '#2E8B57',
  primaryLight: '#E8F5E8',
  
  // Level colors
  beginner: '#4CAF50',
  beginnerLight: '#E8F5E8',
  intermediate: '#FF9800',
  intermediateLight: '#FFF3E0',
  advanced: '#F44336',
  advancedLight: '#FFEBEE',
  
  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray100: '#f5f5f5',
  gray200: '#e0e0e0',
  gray300: '#bdbdbd',
  gray400: '#9e9e9e',
  gray500: '#757575',
  gray600: '#616161',
  gray700: '#424242',
  gray800: '#212121',
  
  // Text colors
  textPrimary: '#333333',
  textSecondary: '#666666',
  textLight: '#999999',
  
  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
};

export const Fonts = {
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 28,
  },
  weights: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

export const BorderRadius = {
  sm: 4,
  md: 8,
  lg: 10,
  xl: 12,
  xxl: 16,
};

export const Shadows = {
  small: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  medium: {
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  large: {
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.27,
    shadowRadius: 4.65,
  },
};

export const Layout = {
  window: {
    width: 0, // Will be set dynamically
    height: 0, // Will be set dynamically
  },
  isSmallDevice: false, // Will be set dynamically
};

// Common styles that can be reused
export const CommonStyles = {
  container: {
    flex: 1,
    backgroundColor: Colors.gray100,
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
    ...Shadows.small,
  },
  headerText: {
    fontSize: Fonts.sizes.xxl,
    fontWeight: Fonts.weights.bold,
    color: Colors.textPrimary,
  },
  bodyText: {
    fontSize: Fonts.sizes.md,
    color: Colors.textPrimary,
    lineHeight: 24,
  },
  captionText: {
    fontSize: Fonts.sizes.sm,
    color: Colors.textSecondary,
  },
};
