import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import type { RouteProp } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { RootStackParamList } from '../navigation/AppNavigator';
import { getLessonById } from '../data/lessons';
import { Colors } from '../constants/theme';
import { useProgress } from '../context/ProgressContext';

type LessonScreenRouteProp = RouteProp<RootStackParamList, 'Lesson'>;
type LessonScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Lesson'>;

export default function LessonScreen() {
  const route = useRoute<LessonScreenRouteProp>();
  const navigation = useNavigation<LessonScreenNavigationProp>();
  const { lessonId, level, title } = route.params;
  const { completeLessonProgress, isLessonCompleted } = useProgress();

  const [currentSection, setCurrentSection] = useState<'vocabulary' | 'phrases' | 'examples'>('vocabulary');
  const [startTime] = useState(Date.now());

  // Get lesson data from our data source
  const lesson = getLessonById(lessonId);

  if (!lesson) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Lesson not found</Text>
      </View>
    );
  }

  const lessonContent = lesson.content;
  const isCompleted = isLessonCompleted(lessonId);

  const renderVocabulary = () => (
    <View style={styles.sectionContent}>
      <Text style={styles.sectionTitle}>Erayada - Vocabulary</Text>
      {lessonContent.vocabulary.map((item, index) => (
        <View key={index} style={styles.vocabularyCard}>
          <Text style={styles.englishWord}>{item.english}</Text>
          <Text style={styles.somaliWord}>{item.somali}</Text>
          <Text style={styles.pronunciation}>/{item.pronunciation}/</Text>
        </View>
      ))}
    </View>
  );

  const renderPhrases = () => (
    <View style={styles.sectionContent}>
      <Text style={styles.sectionTitle}>Weedho - Phrases</Text>
      {lessonContent.phrases.map((item, index) => (
        <View key={index} style={styles.phraseCard}>
          <Text style={styles.englishPhrase}>{item.english}</Text>
          <Text style={styles.somaliPhrase}>{item.somali}</Text>
          {item.context && (
            <Text style={styles.contextText}>Context: {item.context}</Text>
          )}
        </View>
      ))}
    </View>
  );

  const renderExamples = () => (
    <View style={styles.sectionContent}>
      <Text style={styles.sectionTitle}>Tusaalooyin - Examples</Text>
      {lessonContent.examples.map((item, index) => (
        <View key={index} style={styles.exampleCard}>
          <Text style={styles.englishExample}>{item.english}</Text>
          <Text style={styles.somaliExample}>{item.somali}</Text>
          {item.situation && (
            <Text style={styles.situationText}>Situation: {item.situation}</Text>
          )}
        </View>
      ))}
    </View>
  );

  const handleCompleteLesson = async () => {
    if (isCompleted) {
      navigation.goBack();
      return;
    }

    try {
      const timeSpent = Math.round((Date.now() - startTime) / (1000 * 60)); // Convert to minutes
      await completeLessonProgress(lessonId, 100, timeSpent);

      Alert.alert(
        'Cashar la dhammeeyay!',
        'Lesson Completed!',
        [
          {
            text: 'Sii wad - Continue',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Error completing lesson:', error);
      Alert.alert('Error', 'Failed to save progress. Please try again.');
    }
  };

  const getLevelColor = () => {
    switch (level) {
      case 'beginner': return Colors.beginner;
      case 'intermediate': return Colors.intermediate;
      case 'advanced': return Colors.advanced;
      default: return Colors.primary;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, currentSection === 'vocabulary' && styles.activeTab]}
          onPress={() => setCurrentSection('vocabulary')}>
          <Text style={[styles.tabText, currentSection === 'vocabulary' && styles.activeTabText]}>
            Erayada
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, currentSection === 'phrases' && styles.activeTab]}
          onPress={() => setCurrentSection('phrases')}>
          <Text style={[styles.tabText, currentSection === 'phrases' && styles.activeTabText]}>
            Weedho
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, currentSection === 'examples' && styles.activeTab]}
          onPress={() => setCurrentSection('examples')}>
          <Text style={[styles.tabText, currentSection === 'examples' && styles.activeTabText]}>
            Tusaalooyin
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {currentSection === 'vocabulary' && renderVocabulary()}
        {currentSection === 'phrases' && renderPhrases()}
        {currentSection === 'examples' && renderExamples()}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.completeButton, { backgroundColor: getLevelColor() }]}
          onPress={handleCompleteLesson}>
          <Text style={styles.completeButtonText}>
            {isCompleted ? 'Dib u eeg - Review Lesson' : 'Dhammaystir Casharkan - Complete Lesson'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  tab: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#2E8B57',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#2E8B57',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  sectionContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  vocabularyCard: {
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  englishWord: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  somaliWord: {
    fontSize: 20,
    color: '#2E8B57',
    marginBottom: 5,
  },
  pronunciation: {
    fontSize: 16,
    color: '#666',
    fontStyle: 'italic',
  },
  phraseCard: {
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  englishPhrase: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  somaliPhrase: {
    fontSize: 16,
    color: '#2E8B57',
  },
  exampleCard: {
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  englishExample: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    lineHeight: 24,
  },
  somaliExample: {
    fontSize: 15,
    color: '#2E8B57',
    lineHeight: 22,
  },
  footer: {
    padding: 20,
    backgroundColor: '#fff',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  completeButton: {
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorText: {
    fontSize: 18,
    color: '#F44336',
    textAlign: 'center',
    marginTop: 50,
  },
  contextText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 5,
  },
  situationText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 5,
  },
});
