import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { RootStackParamList } from '../navigation/AppNavigator';
import { advancedLessons } from '../data/lessons';
import LessonCard from '../components/LessonCard';
import ProgressBar from '../components/ProgressBar';
import { Colors } from '../constants/theme';

type AdvancedScreenNavigationProp = StackNavigationProp<RootStackParamList>;

export default function AdvancedScreen() {
  const navigation = useNavigation<AdvancedScreenNavigationProp>();

  const completedLessons = advancedLessons.filter(lesson => lesson.completed).length;
  const progressPercentage = (completedLessons / advancedLessons.length) * 100;

  const renderLessonItem = ({ item }: { item: typeof advancedLessons[0] }) => (
    <LessonCard
      title={item.title}
      somaliTitle={item.somaliTitle}
      description={item.description}
      somaliDescription={item.somaliDescription}
      duration={item.duration}
      completed={item.completed}
      levelColor={Colors.advanced}
      onPress={() =>
        navigation.navigate('Lesson', {
          lessonId: item.id,
          level: 'advanced',
          title: item.title,
        })
      }
    />
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Horumar - Advanced</Text>
        <Text style={styles.headerSubtitle}>
          Ku dhamaystir xirfaddaada Ingiriisiga
        </Text>
        <Text style={styles.headerDescription}>
          Master your English skills
        </Text>
      </View>

      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>Horumarkaaga - Your Progress</Text>
        <ProgressBar
          progress={progressPercentage}
          color={Colors.advanced}
          showPercentage={true}
        />
        <Text style={styles.progressLabel}>
          {completedLessons} / {advancedLessons.length} casharrada
        </Text>
      </View>

      <FlatList
        data={advancedLessons}
        renderItem={renderLessonItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.lessonsList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#F44336',
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#FFEBEE',
    marginBottom: 3,
  },
  headerDescription: {
    fontSize: 14,
    color: '#FFEBEE',
  },
  progressContainer: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  progressText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#F44336',
    borderRadius: 4,
  },
  progressLabel: {
    fontSize: 14,
    color: '#666',
  },
  lessonsList: {
    padding: 15,
  },
  lessonCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  completedCard: {
    backgroundColor: '#FFEBEE',
    borderColor: '#F44336',
    borderWidth: 1,
  },
  lessonContent: {
    padding: 20,
  },
  lessonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  lessonTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  lessonDuration: {
    fontSize: 14,
    color: '#F44336',
    fontWeight: 'bold',
  },
  lessonSomaliTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  lessonDescription: {
    fontSize: 14,
    color: '#333',
    marginBottom: 3,
  },
  lessonSomaliDescription: {
    fontSize: 13,
    color: '#666',
  },
  completedBadge: {
    marginTop: 10,
    alignSelf: 'flex-start',
  },
  completedText: {
    fontSize: 12,
    color: '#F44336',
    fontWeight: 'bold',
  },
});
