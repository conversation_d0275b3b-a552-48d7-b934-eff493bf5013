export interface VocabularyItem {
  english: string;
  somali: string;
  pronunciation: string;
  audioUrl?: string;
}

export interface PhraseItem {
  english: string;
  somali: string;
  context?: string;
}

export interface ExampleItem {
  english: string;
  somali: string;
  situation?: string;
}

export interface Exercise {
  id: string;
  type: 'multiple_choice' | 'fill_blank' | 'translation' | 'matching';
  question: string;
  questionSomali: string;
  options?: string[];
  correctAnswer: string;
  explanation?: string;
  explanationSomali?: string;
}

export interface LessonContent {
  vocabulary: VocabularyItem[];
  phrases: PhraseItem[];
  examples: ExampleItem[];
  exercises: Exercise[];
}

export interface Lesson {
  id: string;
  title: string;
  somaliTitle: string;
  description: string;
  somaliDescription: string;
  duration: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  completed: boolean;
  content: LessonContent;
}

// Beginner Lessons Data
export const beginnerLessons: Lesson[] = [
  {
    id: 'b1',
    title: 'Basic Greetings',
    somaliTitle: '<PERSON>aan <PERSON> ah',
    description: 'Learn how to say hello and goodbye',
    somaliDescription: 'Baro sida loo yidhaahdo salam iyo nabadgelyo',
    duration: '10 min',
    level: 'beginner',
    completed: false,
    content: {
      vocabulary: [
        { english: 'Hello', somali: 'Salam', pronunciation: 'heh-LOH' },
        { english: 'Hi', somali: 'Haye', pronunciation: 'HI' },
        { english: 'Good morning', somali: 'Subax wanaagsan', pronunciation: 'good MOR-ning' },
        { english: 'Good afternoon', somali: 'Galab wanaagsan', pronunciation: 'good af-ter-NOON' },
        { english: 'Good evening', somali: 'Fiid wanaagsan', pronunciation: 'good EEV-ning' },
        { english: 'Goodbye', somali: 'Nabadgelyo', pronunciation: 'good-BYE' },
        { english: 'See you later', somali: 'Waan ku arki doonaa', pronunciation: 'see you LAY-ter' },
        { english: 'Nice to meet you', somali: 'Waa farxad in aan ku arko', pronunciation: 'nice to MEET you' },
      ],
      phrases: [
        { english: 'How are you?', somali: 'Sidee tahay?', context: 'Informal greeting' },
        { english: 'I am fine', somali: 'Waan fiicnahay', context: 'Response to greeting' },
        { english: 'How are you doing?', somali: 'Sidee u socotaa?', context: 'Casual greeting' },
        { english: 'What\'s up?', somali: 'Maxaa jira?', context: 'Very casual greeting' },
      ],
      examples: [
        { 
          english: 'Hello, how are you today?', 
          somali: 'Salam, sidee tahay maanta?',
          situation: 'Meeting someone during the day'
        },
        { 
          english: 'Good morning! Nice to meet you.', 
          somali: 'Subax wanaagsan! Waa farxad in aan ku arko.',
          situation: 'First meeting in the morning'
        },
        { 
          english: 'Goodbye, see you later!', 
          somali: 'Nabadgelyo, waan ku arki doonaa!',
          situation: 'Leaving someone you\'ll see again'
        },
      ],
      exercises: [
        {
          id: 'b1_ex1',
          type: 'multiple_choice',
          question: 'How do you say "Hello" in English?',
          questionSomali: 'Sidee loo yidhaa "Salam" Ingiriisiga?',
          options: ['Hello', 'Goodbye', 'Thank you', 'Please'],
          correctAnswer: 'Hello',
          explanation: '"Hello" is the most common greeting in English.',
          explanationSomali: '"Hello" waa salaamka ugu caadiga ah ee Ingiriisiga.'
        },
        {
          id: 'b1_ex2',
          type: 'translation',
          question: 'Translate: "Good morning"',
          questionSomali: 'Turjun: "Subax wanaagsan"',
          correctAnswer: 'Good morning',
          explanation: 'Used to greet someone in the morning.',
          explanationSomali: 'Loo isticmaalaa in lagu salaamo qof subaxda.'
        }
      ]
    }
  },
  {
    id: 'b2',
    title: 'Numbers 1-20',
    somaliTitle: 'Tirooyinka 1-20',
    description: 'Learn basic numbers in English',
    somaliDescription: 'Baro tirooyinka aasaasiga ah ee Ingiriisiga',
    duration: '15 min',
    level: 'beginner',
    completed: false,
    content: {
      vocabulary: [
        { english: 'One', somali: 'Hal', pronunciation: 'wun' },
        { english: 'Two', somali: 'Laba', pronunciation: 'too' },
        { english: 'Three', somali: 'Saddex', pronunciation: 'three' },
        { english: 'Four', somali: 'Afar', pronunciation: 'for' },
        { english: 'Five', somali: 'Shan', pronunciation: 'five' },
        { english: 'Six', somali: 'Lix', pronunciation: 'siks' },
        { english: 'Seven', somali: 'Todobo', pronunciation: 'SEV-en' },
        { english: 'Eight', somali: 'Sideed', pronunciation: 'ayt' },
        { english: 'Nine', somali: 'Sagaal', pronunciation: 'nine' },
        { english: 'Ten', somali: 'Toban', pronunciation: 'ten' },
        { english: 'Eleven', somali: 'Kow iyo toban', pronunciation: 'eh-LEV-en' },
        { english: 'Twelve', somali: 'Laba iyo toban', pronunciation: 'twelv' },
        { english: 'Thirteen', somali: 'Saddex iyo toban', pronunciation: 'thur-TEEN' },
        { english: 'Fourteen', somali: 'Afar iyo toban', pronunciation: 'for-TEEN' },
        { english: 'Fifteen', somali: 'Shan iyo toban', pronunciation: 'fif-TEEN' },
        { english: 'Sixteen', somali: 'Lix iyo toban', pronunciation: 'siks-TEEN' },
        { english: 'Seventeen', somali: 'Todobo iyo toban', pronunciation: 'sev-en-TEEN' },
        { english: 'Eighteen', somali: 'Sideed iyo toban', pronunciation: 'ay-TEEN' },
        { english: 'Nineteen', somali: 'Sagaal iyo toban', pronunciation: 'nine-TEEN' },
        { english: 'Twenty', somali: 'Labaatan', pronunciation: 'TWEN-tee' },
      ],
      phrases: [
        { english: 'How many?', somali: 'Immisa?', context: 'Asking for quantity' },
        { english: 'I have five books', somali: 'Waxaan haystaa shan buug', context: 'Stating quantity' },
        { english: 'There are ten students', somali: 'Waxaa jira toban arday', context: 'Describing quantity' },
      ],
      examples: [
        { 
          english: 'I am twenty years old', 
          somali: 'Waxaan jiraa labaatan sannadood',
          situation: 'Stating your age'
        },
        { 
          english: 'The class has fifteen students', 
          somali: 'Fasalku wuxuu leeyahay shan iyo toban arday',
          situation: 'Describing class size'
        },
      ],
      exercises: [
        {
          id: 'b2_ex1',
          type: 'multiple_choice',
          question: 'What number comes after nine?',
          questionSomali: 'Tiro kee ayaa ka dambaysa sagaal?',
          options: ['Eight', 'Ten', 'Eleven', 'Seven'],
          correctAnswer: 'Ten',
          explanation: 'Ten comes after nine in the number sequence.',
          explanationSomali: 'Toban ayaa ka dambaysa sagaal tirooyinka.'
        }
      ]
    }
  }
];

// Intermediate Lessons Data
export const intermediateLessons: Lesson[] = [
  {
    id: 'i1',
    title: 'Present Tense',
    somaliTitle: 'Wakhtiga Hadda',
    description: 'Learn present tense verb forms',
    somaliDescription: 'Baro qaabka falka wakhtiga hadda',
    duration: '20 min',
    level: 'intermediate',
    completed: false,
    content: {
      vocabulary: [
        { english: 'I am', somali: 'Waan ahay', pronunciation: 'I am' },
        { english: 'You are', somali: 'Waad tahay', pronunciation: 'you are' },
        { english: 'He is', somali: 'Wuu yahay', pronunciation: 'he is' },
        { english: 'She is', somali: 'Way tahay', pronunciation: 'she is' },
        { english: 'We are', somali: 'Waan nahay', pronunciation: 'we are' },
        { english: 'They are', somali: 'Way yihiin', pronunciation: 'they are' },
        { english: 'I have', somali: 'Waan haystaa', pronunciation: 'I have' },
        { english: 'I do', somali: 'Waan sameeyaa', pronunciation: 'I do' },
      ],
      phrases: [
        { english: 'I am learning English', somali: 'Waxaan bartaa Ingiriisiga', context: 'Present continuous' },
        { english: 'She works every day', somali: 'Maalin walba way shaqaysaa', context: 'Present simple' },
        { english: 'We are studying together', somali: 'Waan wada baranayaa', context: 'Present continuous' },
        { english: 'They live in the city', somali: 'Magaalada ayay ku nool yihiin', context: 'Present simple' },
      ],
      examples: [
        {
          english: 'I am a student and I study English every day',
          somali: 'Waxaan ahay arday, maalin walbana waan bartaa Ingiriisiga',
          situation: 'Describing current status and habits'
        },
        {
          english: 'She is working on her computer right now',
          somali: 'Hadda waxay ku shaqaysaa kombiyuutarkeeda',
          situation: 'Describing current action'
        },
      ],
      exercises: [
        {
          id: 'i1_ex1',
          type: 'multiple_choice',
          question: 'Choose the correct present tense: "I ___ English"',
          questionSomali: 'Dooro wakhtiga hadda ee saxda ah: "I ___ English"',
          options: ['learn', 'learned', 'will learn', 'learning'],
          correctAnswer: 'learn',
          explanation: 'Use "learn" for present simple tense.',
          explanationSomali: '"learn" loo isticmaalaa wakhtiga hadda ee fudud.'
        }
      ]
    }
  },
  {
    id: 'i2',
    title: 'Past Tense',
    somaliTitle: 'Wakhtiga Hore',
    description: 'Learn past tense verb forms',
    somaliDescription: 'Baro qaabka falka wakhtiga hore',
    duration: '25 min',
    level: 'intermediate',
    completed: false,
    content: {
      vocabulary: [
        { english: 'I was', somali: 'Waan ahaa', pronunciation: 'I was' },
        { english: 'You were', somali: 'Waad ahayd', pronunciation: 'you were' },
        { english: 'He was', somali: 'Wuu ahaa', pronunciation: 'he was' },
        { english: 'She was', somali: 'Way ahayd', pronunciation: 'she was' },
        { english: 'We were', somali: 'Waan ahayn', pronunciation: 'we were' },
        { english: 'They were', somali: 'Way ahaayeen', pronunciation: 'they were' },
        { english: 'I went', somali: 'Waan tegay', pronunciation: 'I went' },
        { english: 'I did', somali: 'Waan sameeyay', pronunciation: 'I did' },
      ],
      phrases: [
        { english: 'I went to school yesterday', somali: 'Shalay dugsiga ayaan tegay', context: 'Past simple' },
        { english: 'She was studying when I called', somali: 'Markaan wacay way baranaysay', context: 'Past continuous' },
        { english: 'We lived there for five years', somali: 'Shan sannadood ayaan halkaas ku noolnay', context: 'Past simple duration' },
      ],
      examples: [
        {
          english: 'Yesterday I visited my grandmother and we had tea together',
          somali: 'Shalay ayaan booqday ayeeyaday, shaahna waan wada cabnay',
          situation: 'Describing past events'
        },
      ],
      exercises: [
        {
          id: 'i2_ex1',
          type: 'multiple_choice',
          question: 'Choose the correct past tense: "Yesterday I ___ to the market"',
          questionSomali: 'Dooro wakhtiga hore ee saxda ah: "Yesterday I ___ to the market"',
          options: ['go', 'went', 'going', 'will go'],
          correctAnswer: 'went',
          explanation: 'Use "went" for past tense of "go".',
          explanationSomali: '"went" waa wakhtiga hore ee "go".'
        }
      ]
    }
  }
];

// Advanced Lessons Data
export const advancedLessons: Lesson[] = [
  {
    id: 'a1',
    title: 'Complex Grammar Structures',
    somaliTitle: 'Qaabka Naxwaha Adag',
    description: 'Master advanced grammar patterns',
    somaliDescription: 'Ku xirfad qaabka naxwaha horumarsan',
    duration: '30 min',
    level: 'advanced',
    completed: false,
    content: {
      vocabulary: [
        { english: 'Nevertheless', somali: 'Si kastaba ha ahaatee', pronunciation: 'nev-er-the-LESS' },
        { english: 'Furthermore', somali: 'Intaa waxaa dheer', pronunciation: 'FUR-ther-more' },
        { english: 'Consequently', somali: 'Sidaa darteed', pronunciation: 'CON-se-quent-ly' },
        { english: 'Subsequently', somali: 'Ka dib', pronunciation: 'SUB-se-quent-ly' },
        { english: 'Notwithstanding', somali: 'In kasta oo', pronunciation: 'not-with-STAND-ing' },
      ],
      phrases: [
        { english: 'Having completed the project, we celebrated', somali: 'Mashruuca ka dib markaan dhamaysanay, waan dabaaldeganay', context: 'Perfect participle' },
        { english: 'Were I to know earlier, I would have helped', somali: 'Haddaan hore u ogaan lahaa, waan caawi lahaa', context: 'Subjunctive mood' },
        { english: 'Not only did he arrive late, but he also forgot the documents', somali: 'Kaliya ma aha inuu daahay, balse wuxuu kaloo illoobay dukumentiyada', context: 'Inversion after negative expressions' },
      ],
      examples: [
        {
          english: 'Despite having studied extensively, the examination proved more challenging than anticipated',
          somali: 'In kasta oo aad loo baray, imtixaanku wuxuu noqday mid ka adag sidii la filayay',
          situation: 'Academic or formal writing'
        },
      ],
      exercises: [
        {
          id: 'a1_ex1',
          type: 'multiple_choice',
          question: 'Choose the correct advanced structure: "___ the weather, we will proceed with the event"',
          questionSomali: 'Dooro qaabka horumarsan ee saxda ah: "___ the weather, we will proceed with the event"',
          options: ['Despite', 'Because', 'When', 'If'],
          correctAnswer: 'Despite',
          explanation: '"Despite" is used to show contrast with a following noun or gerund.',
          explanationSomali: '"Despite" waxaa loo isticmaalaa in lagu muujiyo kala duwanaansho.'
        }
      ]
    }
  },
  {
    id: 'a2',
    title: 'Idioms & Expressions',
    somaliTitle: 'Maahmaahyo iyo Tibaaxo',
    description: 'Learn common English idioms',
    somaliDescription: 'Baro maahmaahyada Ingiriisiga caadiga ah',
    duration: '25 min',
    level: 'advanced',
    completed: false,
    content: {
      vocabulary: [
        { english: 'Break the ice', somali: 'Bilaabi wadahadal', pronunciation: 'break the ice' },
        { english: 'Hit the nail on the head', somali: 'Si sax ah u qabo', pronunciation: 'hit the nail on the head' },
        { english: 'Spill the beans', somali: 'Sirta daaha ka qaad', pronunciation: 'spill the beans' },
        { english: 'Bite the bullet', somali: 'Adkayso oo aqbal', pronunciation: 'bite the bullet' },
        { english: 'Cut to the chase', somali: 'Toos u gal arrintu', pronunciation: 'cut to the chase' },
      ],
      phrases: [
        { english: 'It\'s raining cats and dogs', somali: 'Roob xoog leh ayaa da\'aya', context: 'Weather idiom' },
        { english: 'Don\'t count your chickens before they hatch', somali: 'Ha tirin digaagaaga intaanay bixin', context: 'Advice idiom' },
        { english: 'The ball is in your court', somali: 'Hadda waa kaa go\'an', context: 'Decision-making idiom' },
      ],
      examples: [
        {
          english: 'When she said the presentation was "a piece of cake," I knew she had prepared well',
          somali: 'Markay tidhi bandhiggu waa "sahlan", waan ogaaday inay si fiican u diyaarisay',
          situation: 'Using idioms in conversation'
        },
      ],
      exercises: [
        {
          id: 'a2_ex1',
          type: 'multiple_choice',
          question: 'What does "break the ice" mean?',
          questionSomali: 'Maxay ka dhigan tahay "break the ice"?',
          options: ['Start a conversation', 'Break something', 'Feel cold', 'Stop talking'],
          correctAnswer: 'Start a conversation',
          explanation: '"Break the ice" means to initiate conversation in a social situation.',
          explanationSomali: '"Break the ice" waxay ka dhigan tahay in la bilaabo wadahadal xaalad bulsheed ah.'
        }
      ]
    }
  }
];

// Function to get lesson by ID
export const getLessonById = (id: string): Lesson | undefined => {
  const allLessons = [...beginnerLessons, ...intermediateLessons, ...advancedLessons];
  return allLessons.find(lesson => lesson.id === id);
};

// Function to get lessons by level
export const getLessonsByLevel = (level: 'beginner' | 'intermediate' | 'advanced'): Lesson[] => {
  switch (level) {
    case 'beginner':
      return beginnerLessons;
    case 'intermediate':
      return intermediateLessons;
    case 'advanced':
      return advancedLessons;
    default:
      return [];
  }
};
