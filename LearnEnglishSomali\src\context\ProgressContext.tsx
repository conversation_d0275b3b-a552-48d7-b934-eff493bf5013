import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import StorageService, { UserProgress, UserStats } from '../services/StorageService';

interface ProgressContextType {
  userProgress: UserProgress[];
  userStats: UserStats;
  isLessonCompleted: (lessonId: string) => boolean;
  completeLessonProgress: (lessonId: string, score?: number, timeSpent?: number) => Promise<void>;
  refreshProgress: () => Promise<void>;
  loading: boolean;
}

const ProgressContext = createContext<ProgressContextType | undefined>(undefined);

interface ProgressProviderProps {
  children: ReactNode;
}

export const ProgressProvider: React.FC<ProgressProviderProps> = ({ children }) => {
  const [userProgress, setUserProgress] = useState<UserProgress[]>([]);
  const [userStats, setUserStats] = useState<UserStats>({
    totalLessonsCompleted: 0,
    totalTimeSpent: 0,
    currentStreak: 0,
    longestStreak: 0,
    achievements: [],
  });
  const [loading, setLoading] = useState(true);

  const loadUserData = async () => {
    try {
      setLoading(true);
      const [progress, stats] = await Promise.all([
        StorageService.getUserProgress(),
        StorageService.getUserStats(),
      ]);
      setUserProgress(progress);
      setUserStats(stats);
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUserData();
  }, []);

  const isLessonCompleted = (lessonId: string): boolean => {
    return userProgress.some(p => p.lessonId === lessonId && p.completed);
  };

  const completeLessonProgress = async (lessonId: string, score?: number, timeSpent?: number): Promise<void> => {
    try {
      await StorageService.updateLessonProgress(lessonId, true, score, timeSpent);
      await refreshProgress();
    } catch (error) {
      console.error('Error completing lesson:', error);
    }
  };

  const refreshProgress = async (): Promise<void> => {
    await loadUserData();
  };

  const contextValue: ProgressContextType = {
    userProgress,
    userStats,
    isLessonCompleted,
    completeLessonProgress,
    refreshProgress,
    loading,
  };

  return (
    <ProgressContext.Provider value={contextValue}>
      {children}
    </ProgressContext.Provider>
  );
};

export const useProgress = (): ProgressContextType => {
  const context = useContext(ProgressContext);
  if (context === undefined) {
    throw new Error('useProgress must be used within a ProgressProvider');
  }
  return context;
};
